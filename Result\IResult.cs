﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Result
{
    public class IResult
    {
        public string ETA { get; set; }
        public string ETAExc { get; set; }
        public BindingList<IResultContainer> ContainerList { get; set; }
        public string ContainerExc { get; set; }
    }
}
